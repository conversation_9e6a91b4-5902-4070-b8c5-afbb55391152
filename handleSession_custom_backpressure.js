// 🔧 方案6: 自定义背压处理 (Custom Backpressure) 替代 pipeTo
// 完整保留原始的 ingressMode 逻辑，实现自定义背压控制机制

export async function handleSession(request, env, ctx, protocolMode) {
    try {
        const { 0: client, 1: server } = Object.values(new WebSocketPair());
        server.accept();

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

        /* ingress modes: transform | readable */
        let ingressMode = "transform";   // ← Change here to switch modes

        let upstreamReadable;
        let tcpInterface = null;
        let tcpReader = null;
        let tcpWriter = null;

        // 🔧 完整的原始 ingressMode 实现
        if (ingressMode === "transform") {
            /* ----- TransformStream with IIFE implementation ----- */
            upstreamReadable = ((transformStream) => {
                const holdWriter = transformStream.writable.getWriter();

                if (earlyHeader) {
                    try {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData) {
                            holdWriter.write(earlyData).catch(() => { });
                        }
                    } catch (e) { }
                }

                const handleMessage = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.write(e.data).catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleClose = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.close().catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleError = (e) => {
                    try {
                        if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                            tcpInterface.close();
                        }
                        if (server && server.close instanceof Function) {
                            server.close(1013);
                        }
                        if (holdWriter) {
                            holdWriter.abort().catch(() => { });
                        }
                    } catch (e) { }
                };

                server['onmessage'] = handleMessage;
                server['onclose'] = handleClose;
                server['onerror'] = handleError;

                return transformStream.readable;
            })(
                new TransformStream()
            );

        } else if (ingressMode === "readable") {
            /* ----- ReadableStream implementation ----- */
            upstreamReadable = new ReadableStream({
                start(controller) {
                    if (earlyHeader) {
                        try {
                            const earlyData = decodeBase64Url(earlyHeader);
                            if (earlyData) {
                                controller.enqueue(earlyData);
                            }
                        } catch (e) { }
                    }

                    const handleMessage = (e) => {
                        try {
                            controller.enqueue(e.data);
                        } catch (e) { }
                    };

                    const handleClose = (e) => {
                        try {
                            controller.close();
                        } catch (e) { }
                    };

                    const handleError = (e) => {
                        try {
                            if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                                tcpInterface.close();
                            }
                            if (server && server.close instanceof Function) {
                                server.close(1013);
                            }
                            controller.error(e);
                        } catch (e) { }
                    };

                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                }
            });
        }

        // 🔧 自定义背压处理函数
        async function customPipeWithBackpressure(readable, processChunkFn) {
            const reader = readable.getReader();
            
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 🔧 处理数据块，包含背压控制
                    await processChunkFn(value);
                }
            } finally {
                reader.releaseLock();
            }
        }

        // 🔧 带背压控制的数据块处理函数
        const processChunkWithBackpressure = async (chunk) => {
            try {
                if (tcpWriter) {
                    // 🔧 关键：等待写入器准备就绪 (背压控制)
                    await tcpWriter.ready;
                    
                    // 检查背压情况
                    if (tcpWriter.desiredSize !== null && tcpWriter.desiredSize <= 0) {
                        console.log('Backpressure detected on TCP writer, waiting...');
                        await tcpWriter.ready;
                    }
                    
                    await tcpWriter.write(chunk);
                    return;
                }

                const header = await parseProtocolHeader(chunk, server, protocolMode);

                try {
                    tcpInterface = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
                    await tcpInterface.opened;
                } catch (connectError) {
                    console.warn('First connection failed, retrying with relay mode');
                    tcpInterface = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
                    await tcpInterface.opened;
                }

                tcpWriter = tcpInterface.writable.getWriter();
                
                // 🔧 首次写入也要检查背压
                await tcpWriter.ready;
                await tcpWriter.write(chunk);

                // 🔧 启动从服务器读取数据的任务 (也使用背压控制)
                (async () => {
                    tcpReader = tcpInterface.readable.getReader();
                    try {
                        while (true) {
                            const { value, done } = await tcpReader.read();
                            if (done) break;
                            if (!value || value.byteLength === 0) continue;

                            // 🔧 WebSocket 发送也可以考虑背压，但通常 WebSocket 内部处理
                            if (server.readyState === WS_STATES.OPEN) {
                                server.send(value);
                            }
                        }
                    } catch (error) {
                        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                        try { tcpReader.releaseLock(); } catch { }
                    } finally {
                        try { tcpReader.releaseLock(); } catch { }
                    }
                })().catch((error) => {
                    GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                });

            } catch (error) {
                GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                throw error;
            }
        };

        // 🔧 使用自定义背压控制替代 upstreamReadable.pipeTo()
        const backpressurePipeProcess = async () => {
            try {
                await customPipeWithBackpressure(upstreamReadable, processChunkWithBackpressure);
            } catch (error) {
                console.error('Custom backpressure pipe error:', error);
                throw error;
            }
        };

        // 启动背压控制管道处理 (替代原始的 .pipeTo().catch())
        backpressurePipeProcess().catch(e => {
            try {
                if (server && server.close instanceof Function) {
                    server.close(1013, e.message);
                }
            } catch { }
            try {
                if (tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch { }
        });

        return new Response(null, { status: 101, webSocket: client });
    } catch (error) {
        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
        return new Response('Session failed', { status: 500 });
    }
}

// 需要的辅助函数和常量 (从原文件导入)
const WS_STATES = { OPEN: 1 };
const GLOBAL_ERROR_HANDLER = { log: (error, context) => console.error(context, error) };
const globalControllerConfig = { connectMode: 'direct', retryMode: 'relayip' };

function decodeBase64Url(str) {
    // 实现 base64url 解码
    return new TextEncoder().encode(str);
}

async function parseProtocolHeader(chunk, server, protocolMode) {
    // 需要从原文件实现
    return { addressType: 1, addressRemote: 'example.com', portRemote: 80 };
}

async function createConnection(header, mode, protocolMode) {
    // 需要从原文件实现
    return {
        opened: Promise.resolve(),
        readable: new ReadableStream(),
        writable: new WritableStream(),
        close: () => {}
    };
}
