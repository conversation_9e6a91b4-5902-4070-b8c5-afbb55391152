// 🔧 精确替换版本 - 只替换 pipeTo 调用，其他完全不变
// 这个版本应该与原始版本行为完全一致

export async function handleSession(request, env, ctx, protocolMode) {
    try {
        const { 0: client, 1: server } = Object.values(new WebSocketPair());
        server.accept();

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

        /* ingress modes: transform | readable */
        let ingressMode = "transform";   // ← Change here to switch modes

        let upstreamReadable;
        let tcpInterface = null;
        let tcpReader = null;
        let tcpWriter = null;

        if (ingressMode === "transform") {
            /* ----- TransformStream with IIFE implementation ----- */
            upstreamReadable = ((transformStream) => {
                const holdWriter = transformStream.writable.getWriter();

                if (earlyHeader) {
                    try {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData) {
                            holdWriter.write(earlyData).catch(() => { });
                        }
                    } catch (e) { }
                }

                const handleMessage = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.write(e.data).catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleClose = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.close().catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleError = (e) => {
                    try {
                        if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                            tcpInterface.close();
                        }
                        if (server && server.close instanceof Function) {
                            server.close(1013);
                        }
                        if (holdWriter) {
                            holdWriter.abort().catch(() => { });
                        }
                    } catch (e) { }
                };

                server['onmessage'] = handleMessage;
                server['onclose'] = handleClose;
                server['onerror'] = handleError;

                return transformStream.readable;
            })(
                new TransformStream()
            );

        } else if (ingressMode === "readable") {
            /* ----- ReadableStream implementation ----- */
            upstreamReadable = new ReadableStream({
                start(controller) {
                    if (earlyHeader) {
                        try {
                            const earlyData = decodeBase64Url(earlyHeader);
                            if (earlyData) {
                                controller.enqueue(earlyData);
                            }
                        } catch (e) { }
                    }

                    const handleMessage = (e) => {
                        try {
                            controller.enqueue(e.data);
                        } catch (e) { }
                    };

                    const handleClose = (e) => {
                        try {
                            controller.close();
                        } catch (e) { }
                    };

                    const handleError = (e) => {
                        try {
                            if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                                tcpInterface.close();
                            }
                            if (server && server.close instanceof Function) {
                                server.close(1013);
                            }
                            controller.error(e);
                        } catch (e) { }
                    };

                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                }
            });
        }

        // 🔧 正确的替换方式：完全模拟原始 pipeTo 的 WritableStream.write() 逻辑
        (async () => {
            const reader = upstreamReadable.getReader();
            try {
                while (true) {
                    const { done, value: chunk } = await reader.read();
                    if (done) break;

                    // 🔧 完全复制原始 WritableStream.write() 的逻辑
                    try {
                        if (tcpWriter) {
                            await tcpWriter.write(chunk);
                            continue; // 重要：继续处理下一个数据块
                        }

                        // 第一个数据块：解析协议头并建立连接
                        const header = await parseProtocolHeader(chunk, server, protocolMode);

                        try {
                            tcpInterface = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                        } catch (connectError) {
                            console.warn('First connection failed, retrying with relay mode');
                            tcpInterface = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
                            await tcpInterface.opened;
                        }

                        tcpWriter = tcpInterface.writable.getWriter();

                        // 发送协议响应（如果需要）
                        if (protocolMode === globalControllerConfig.targetProtocolType0) {
                            server.send(Uint8Array.of(header.version, 0));
                        }

                        // 写入原始客户端数据
                        if (header.rawClientData) {
                            await tcpWriter.write(header.rawClientData);
                        }

                        // 🔧 启动从服务器读取数据的任务 (完全复制原始逻辑)
                        (async () => {
                            tcpReader = tcpInterface.readable.getReader();
                            try {
                                while (true) {
                                    const { value, done } = await tcpReader.read();
                                    if (done) break;
                                    if (!value || value.byteLength === 0) continue;

                                    if (server.readyState === WS_STATES.OPEN) {
                                        server.send(value);
                                    }
                                }
                            } catch (error) {
                                GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                                try { tcpReader.releaseLock(); } catch { }
                            } finally {
                                try { tcpReader.releaseLock(); } catch { }
                            }
                        })().catch((error) => {
                            GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                        });

                    } catch (error) {
                        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                        throw error;
                    }
                }
            } finally {
                reader.releaseLock();
            }
        })().catch(e => {
            try {
                if (server && server.close instanceof Function) {
                    server.close(1013, e.message);
                }
            } catch { }
            try {
                if (tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch { }
        });

        return new Response(null, { status: 101, webSocket: client });
    } catch (error) {
        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
        return new Response('Session failed', { status: 500 });
    }
}

// 🔧 需要从原文件导入的依赖
const WS_STATES = { OPEN: 1 };
const GLOBAL_ERROR_HANDLER = { log: (error, context) => console.error(context, error) };
const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip'
};

function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

// 这些函数需要从原文件完整导入
async function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    // 完整实现需要从 _worker.js 复制
    throw new Error('需要从原文件导入 parseProtocolHeader 的完整实现');
}

async function createConnection(header, mode, protocolMode) {
    // 完整实现需要从 _worker.js 复制
    throw new Error('需要从原文件导入 createConnection 的完整实现');
}
